#MicroXplorer Configuration settings - do not modify
CAD.formats=
CAD.pinconfig=
CAD.provider=
File.Version=6
GPIO.groupedBy=
KeepUserPlacement=false
Mcu.CPN=STM32F103C8T6
Mcu.Family=STM32F1
Mcu.IP0=NVIC
Mcu.IP1=RCC
Mcu.IP2=SPI2
Mcu.IP3=SYS
Mcu.IP4=USART3
Mcu.IPNb=5
Mcu.Name=STM32F103C(8-B)Tx
Mcu.Package=LQFP48
Mcu.Pin0=PC13-TAMPER-RTC
Mcu.Pin1=PC14-OSC32_IN
Mcu.Pin10=PA5
Mcu.Pin11=PA6
Mcu.Pin12=PA7
Mcu.Pin13=PB0
Mcu.Pin14=PB1
Mcu.Pin15=PB10
Mcu.Pin16=PB11
Mcu.Pin17=PB12
Mcu.Pin18=PB13
Mcu.Pin19=PB14
Mcu.Pin2=PC15-OSC32_OUT
Mcu.Pin20=PB15
Mcu.Pin21=PA13
Mcu.Pin22=PA14
Mcu.Pin23=PA15
Mcu.Pin24=PB8
Mcu.Pin25=PB9
Mcu.Pin26=VP_SYS_VS_Systick
Mcu.Pin3=PD0-OSC_IN
Mcu.Pin4=PD1-OSC_OUT
Mcu.Pin5=PA0-WKUP
Mcu.Pin6=PA1
Mcu.Pin7=PA2
Mcu.Pin8=PA3
Mcu.Pin9=PA4
Mcu.PinsNb=27
Mcu.ThirdPartyNb=0
Mcu.UserConstants=
Mcu.UserName=STM32F103C8Tx
MxCube.Version=6.14.1
MxDb.Version=DB.6.0.141
NVIC.BusFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.DebugMonitor_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.ForceEnableDMAVector=true
NVIC.HardFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.MemoryManagement_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.NonMaskableInt_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.PendSV_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.PriorityGroup=NVIC_PRIORITYGROUP_4
NVIC.SVCall_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.SysTick_IRQn=true\:15\:0\:false\:false\:true\:false\:true\:false
NVIC.UsageFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
PA0-WKUP.GPIOParameters=GPIO_Label
PA0-WKUP.GPIO_Label=Motor_L
PA0-WKUP.Locked=true
PA0-WKUP.Signal=S_TIM2_CH1_ETR
PA1.GPIOParameters=GPIO_Label
PA1.GPIO_Label=Motor_R
PA1.Locked=true
PA1.Signal=S_TIM2_CH2
PA13.Mode=Serial_Wire
PA13.Signal=SYS_JTMS-SWDIO
PA14.Mode=Serial_Wire
PA14.Signal=SYS_JTCK-SWCLK
PA15.GPIOParameters=GPIO_Label
PA15.GPIO_Label=Button
PA15.Locked=true
PA15.Signal=GPIO_Output
PA2.Locked=true
PA2.Signal=S_TIM2_CH3
PA3.Locked=true
PA3.Signal=S_TIM2_CH4
PA4.Locked=true
PA4.Signal=GPIO_Input
PA5.Locked=true
PA5.Signal=GPIO_Input
PA6.Locked=true
PA6.Signal=GPIO_Output
PA7.Locked=true
PA7.Signal=GPIO_Output
PB0.Locked=true
PB0.Signal=S_TIM3_CH3
PB1.Locked=true
PB1.Signal=S_TIM3_CH4
PB10.Locked=true
PB10.Mode=Asynchronous
PB10.Signal=USART3_TX
PB11.Locked=true
PB11.Mode=Asynchronous
PB11.Signal=USART3_RX
PB12.GPIOParameters=GPIO_Label
PB12.GPIO_Label=CS
PB12.Locked=true
PB12.Signal=GPIO_Output
PB13.Mode=Full_Duplex_Master
PB13.Signal=SPI2_SCK
PB14.Mode=Full_Duplex_Master
PB14.Signal=SPI2_MISO
PB15.Mode=Full_Duplex_Master
PB15.Signal=SPI2_MOSI
PB8.GPIOParameters=GPIO_Label
PB8.GPIO_Label=Echo1
PB8.Locked=true
PB8.Signal=GPIO_Input
PB9.GPIOParameters=GPIO_Label
PB9.GPIO_Label=Triggle1
PB9.Locked=true
PB9.Signal=GPIO_Output
PC13-TAMPER-RTC.GPIOParameters=GPIO_Label
PC13-TAMPER-RTC.GPIO_Label=LED
PC13-TAMPER-RTC.Locked=true
PC13-TAMPER-RTC.Signal=GPIO_Output
PC14-OSC32_IN.Mode=LSE-External-Oscillator
PC14-OSC32_IN.Signal=RCC_OSC32_IN
PC15-OSC32_OUT.Mode=LSE-External-Oscillator
PC15-OSC32_OUT.Signal=RCC_OSC32_OUT
PD0-OSC_IN.Mode=HSE-External-Oscillator
PD0-OSC_IN.Signal=RCC_OSC_IN
PD1-OSC_OUT.Mode=HSE-External-Oscillator
PD1-OSC_OUT.Signal=RCC_OSC_OUT
PinOutPanel.RotationAngle=0
ProjectManager.AskForMigrate=true
ProjectManager.BackupPrevious=false
ProjectManager.CompilerLinker=GCC
ProjectManager.CompilerOptimize=6
ProjectManager.ComputerToolchain=false
ProjectManager.CoupleFile=false
ProjectManager.CustomerFirmwarePackage=
ProjectManager.DefaultFWLocation=true
ProjectManager.DeletePrevious=true
ProjectManager.DeviceId=STM32F103C8Tx
ProjectManager.FirmwarePackage=STM32Cube FW_F1 V1.8.6
ProjectManager.FreePins=false
ProjectManager.HalAssertFull=false
ProjectManager.HeapSize=0x200
ProjectManager.KeepUserCode=true
ProjectManager.LastFirmware=true
ProjectManager.LibraryCopy=0
ProjectManager.MainLocation=Core/Src
ProjectManager.NoMain=false
ProjectManager.PreviousToolchain=
ProjectManager.ProjectBuild=false
ProjectManager.ProjectFileName=four.ioc
ProjectManager.ProjectName=four
ProjectManager.ProjectStructure=
ProjectManager.RegisterCallBack=
ProjectManager.StackSize=0x400
ProjectManager.TargetToolchain=EWARM V8.32
ProjectManager.ToolChainLocation=
ProjectManager.UAScriptAfterPath=
ProjectManager.UAScriptBeforePath=
ProjectManager.UnderRoot=false
ProjectManager.functionlistsort=1-SystemClock_Config-RCC-false-HAL-false,2-MX_GPIO_Init-GPIO-false-HAL-true,3-MX_SPI2_Init-SPI2-false-HAL-true,4-MX_USART3_UART_Init-USART3-false-HAL-true
RCC.APB1Freq_Value=8000000
RCC.APB2Freq_Value=8000000
RCC.FamilyName=M
RCC.IPParameters=APB1Freq_Value,APB2Freq_Value,FamilyName,PLLCLKFreq_Value,PLLMCOFreq_Value,TimSysFreq_Value
RCC.PLLCLKFreq_Value=8000000
RCC.PLLMCOFreq_Value=4000000
RCC.TimSysFreq_Value=8000000
SH.S_TIM2_CH1_ETR.0=TIM2_CH1
SH.S_TIM2_CH1_ETR.ConfNb=1
SH.S_TIM2_CH2.0=TIM2_CH2
SH.S_TIM2_CH2.ConfNb=1
SH.S_TIM2_CH3.0=TIM2_CH3
SH.S_TIM2_CH3.ConfNb=1
SH.S_TIM2_CH4.0=TIM2_CH4
SH.S_TIM2_CH4.ConfNb=1
SH.S_TIM3_CH3.0=TIM3_CH3
SH.S_TIM3_CH3.ConfNb=1
SH.S_TIM3_CH4.0=TIM3_CH4
SH.S_TIM3_CH4.ConfNb=1
SPI2.CalculateBaudRate=4.0 MBits/s
SPI2.Direction=SPI_DIRECTION_2LINES
SPI2.IPParameters=VirtualType,Mode,Direction,CalculateBaudRate
SPI2.Mode=SPI_MODE_MASTER
SPI2.VirtualType=VM_MASTER
USART3.IPParameters=VirtualMode
USART3.VirtualMode=VM_ASYNC
VP_SYS_VS_Systick.Mode=SysTick
VP_SYS_VS_Systick.Signal=SYS_VS_Systick
board=custom
