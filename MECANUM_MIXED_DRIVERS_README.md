# 混合驱动器四轮麦克纳姆轮系统

这个项目实现了一个使用 DRV8833 和 TM6612 混合驱动器的四轮麦克纳姆轮驱动系统。

## 🔧 硬件配置

### 驱动器分配

- **前轮**: 使用 DRV8833 驱动器
- **后轮**: 使用 TM6612 驱动器

### STM32F103C8T6 引脚连接

```
电机位置        驱动器    STM32引脚    驱动器引脚    功能
--------------------------------------------------------
前左轮 (FL)     DRV8833   PA0         AIN1         前左电机 IN1 (PWM)
前左轮 (FL)     DRV8833   PA1         AIN2         前左电机 IN2 (PWM)
前右轮 (FR)     DRV8833   PA2         BIN1         前右电机 IN1 (PWM)
前右轮 (FR)     DRV8833   PA3         BIN2         前右电机 IN2 (PWM)

后左轮 (BL)     TB6612    PB0         AIN1         后左电机方向控制1
后左轮 (BL)     TB6612    PB1         AIN2         后左电机方向控制2
后左轮 (BL)     TB6612    PA6         PWMA         后左电机速度控制 (PWM)

后右轮 (BR)     TB6612    PB10        BIN1         后右电机方向控制1
后右轮 (BR)     TB6612    PB11        BIN2         后右电机方向控制2
后右轮 (BR)     TB6612    PA7         PWMB         后右电机速度控制 (PWM)
```

### 重要说明

- **DRV8833**: 使用 PWM 信号直接控制 IN1/IN2 引脚来实现方向和速度控制
- **TB6612**: 使用数字信号控制 IN1/IN2 引脚设置方向，使用独立的 PWM 引脚控制速度

### 定时器分配

- **TIM2**: 控制前轮 (DRV8833)
- **TIM3**: 控制后轮 (TM6612)

## 🚗 麦克纳姆轮运动模式

系统支持以下运动模式：

### 基本运动

1. **向前运动** - 所有轮子向前转动
2. **向后运动** - 所有轮子向后转动
3. **向左平移** - 前左/后右轮后退，前右/后左轮前进
4. **向右平移** - 前左/后右轮前进，前右/后左轮后退

### 旋转运动

5. **顺时针旋转** - 左轮前进，右轮后退
6. **逆时针旋转** - 左轮后退，右轮前进

### 对角线运动

7. **左前对角线** - 前右轮和后左轮运动
8. **右前对角线** - 前左轮和后右轮运动
9. **左后对角线** - 前左轮和后右轮运动 (反向)
10. **右后对角线** - 前右轮和后左轮运动 (反向)

## 📁 代码结构

### 核心模块

1. **`src/drv8833.rs`** - DRV8833 驱动器实现
2. **`src/tm6612.rs`** - TM6612 驱动器实现
3. **`src/motor_driver.rs`** - 统一驱动器接口和麦克纳姆轮控制
4. **`src/main.rs`** - 主程序和运动演示

### 关键特性

- **统一接口**: `MotorDriver` trait 提供统一的电机控制接口
- **驱动器包装**: `Drv8833Wrapper` 和 `Tm6612Wrapper` 实现接口适配
- **麦克纳姆控制**: `MecanumDrive` 实现复杂的麦克纳姆轮运动算法
- **错误处理**: 完整的错误处理和日志记录

## 🔄 运动演示序列

主程序按以下顺序演示各种运动模式：

```rust
1. 向前运动 (3秒)
2. 向后运动 (3秒)
3. 向左平移 (3秒)
4. 向右平移 (3秒)
5. 顺时针旋转 (2秒)
6. 逆时针旋转 (2秒)
7. 左前对角线运动 (2秒)
8. 右前对角线运动 (2秒)
```

每个运动之间都有 1 秒的停止间隔。

## ⚡ 电源要求

- **逻辑电源**: 3.3V (STM32)
- **电机电源**:
  - DRV8833: 6-10.8V
  - TM6612: 6-12V
- **电流**: 每个驱动器最大 1.2A 每通道

## 🛠️ 编译和运行

```bash
# 检查代码
cargo check

# 编译
cargo build

# 烧录到STM32
cargo run
```

## 🔍 调试信息

程序提供详细的调试信息：

- 初始化状态
- 每个运动模式的执行状态
- 错误信息和成功确认
- 运动周期完成提示

## 📝 注意事项

1. **接线检查**: 确保所有 PWM 引脚连接正确
2. **电源稳定**: 为两个驱动器提供稳定的电机电源
3. **散热**: 高负载时注意驱动器散热
4. **麦克纳姆轮安装**: 确保麦克纳姆轮按正确方向安装

## 🔧 自定义配置

可以通过修改以下参数来调整系统：

- **PWM 频率**: 在 `Hertz(10_000)` 处修改
- **运动速度**: 在 `move_direction()` 调用中修改速度参数
- **运动时间**: 修改 `Timer::after()` 的持续时间
- **引脚分配**: 在初始化部分修改引脚配置

## 🚀 扩展功能

基于这个系统可以添加：

- 遥控功能
- 传感器反馈 (编码器、IMU)
- PID 速度控制
- 路径规划算法
- 自动导航功能
