#![no_std]
#![no_main]

mod drv8833;
mod motor_driver;
mod tm6612;

use defmt::*;
use drv8833::Drv8833Single;
use embassy_executor::Spawner;
use embassy_stm32::Config;
use embassy_stm32::gpio::{Level, Output, Speed};
use embassy_stm32::time::Hertz;
use embassy_stm32::timer::simple_pwm::{PwmPin, SimplePwm};
use embassy_time::{Duration, Timer};
use motor_driver::{Drv8833Wrapper, MecanumDirection, MecanumDrive, Tm6612Wrapper};
use tm6612::Tm6612Single;
use {defmt_rtt as _, panic_probe as _};

#[embassy_executor::main]
async fn main(_spawner: Spawner) {
    let p = embassy_stm32::init(Config::default());

    info!("🚗 四轮麦克纳姆驱动系统初始化开始");

    let fl_in1 = Output::new(p.PA4, Level::Low, Speed::Low);
    let fl_in2 = Output::new(p.PA5, Level::Low, Speed::Low);
    let fl_pwm_pin = PwmPin::new(p.PB0, embassy_stm32::gpio::OutputType::PushPull);

    let fr_in1 = Output::new(p.PA7, Level::Low, Speed::Low);
    let fr_in2 = Output::new(p.PA6, Level::Low, Speed::Low);
    let fr_pwm_pin = PwmPin::new(p.PB1, embassy_stm32::gpio::OutputType::PushPull);

    let bl_pwm_pin1 = PwmPin::new(p.PA1, embassy_stm32::gpio::OutputType::PushPull);
    let bl_pwm_pin2 = PwmPin::new(p.PA0, embassy_stm32::gpio::OutputType::PushPull);

    let br_pwm_pin1 = PwmPin::new(p.PA3, embassy_stm32::gpio::OutputType::PushPull);
    let br_pwm_pin2 = PwmPin::new(p.PA2, embassy_stm32::gpio::OutputType::PushPull);

    let pwm_tim2 = SimplePwm::new(
        p.TIM2,
        Some(bl_pwm_pin2),
        Some(bl_pwm_pin1),
        Some(br_pwm_pin2),
        Some(br_pwm_pin1),
        Hertz(20_000),
        Default::default(),
    );

    let pwm_tim3 = SimplePwm::new(
        p.TIM3,
        None,
        None,
        Some(fl_pwm_pin),
        Some(fr_pwm_pin),
        Hertz(20_000),
        Default::default(),
    );

    let tim3_chs = pwm_tim3.split();
    let tim2_chs = pwm_tim2.split();

    let mut fl_pwm = tim3_chs.ch3;
    let mut fr_pwm = tim3_chs.ch4;
    let mut bl_pwm_in1 = tim2_chs.ch2;
    let mut bl_pwm_in2 = tim2_chs.ch1;
    let mut br_pwm_in1 = tim2_chs.ch4;
    let mut br_pwm_in2 = tim2_chs.ch3;
    fl_pwm.enable();
    fr_pwm.enable();
    bl_pwm_in2.enable();
    bl_pwm_in1.enable();
    br_pwm_in1.enable();
    br_pwm_in2.enable();

    let front_left_tm = Tm6612Single::new(fl_in1, fl_in2, fl_pwm);
    let front_right_tm = Tm6612Single::new(fr_in1, fr_in2, fr_pwm);
    let back_left_drv = Drv8833Single::new(bl_pwm_in2, bl_pwm_in1);
    let back_right_drv = Drv8833Single::new(br_pwm_in1, br_pwm_in2);

    let front_left = Tm6612Wrapper::new(front_left_tm);
    let front_right = Tm6612Wrapper::new(front_right_tm);
    let back_left = Drv8833Wrapper::new(back_left_drv);
    let back_right = Drv8833Wrapper::new(back_right_drv);

    let mut mecanum: MecanumDrive<
        Tm6612Wrapper<'_, embassy_stm32::peripherals::TIM3>,
        Tm6612Wrapper<'_, embassy_stm32::peripherals::TIM3>,
        Drv8833Wrapper<embassy_stm32::peripherals::TIM2>,
        Drv8833Wrapper<embassy_stm32::peripherals::TIM2>,
    > = MecanumDrive::new(front_left, front_right, back_left, back_right);

    info!("✅ 四轮麦克纳姆驱动系统初始化完成");
    info!("🔧 前轮使用TB6612，后轮使用DRV8833");
    info!("🎯 开始运动演示...");

    match mecanum.move_direction(MecanumDirection::Forward, 80) {
        Ok(_) => info!("  ✅ 直走成功"),
        Err(e) => error!("  ❌ 精细右平移失败: {:?}", e),
    }
    Timer::after(Duration::from_millis(2000)).await;

    match mecanum.strafe_with_speed(MecanumDirection::Left, 55, 80) {
        Ok(_) => info!("  ✅ 精细左平移成功"),
        Err(e) => error!("  ❌ 精细右平移失败: {:?}", e),
    }
    Timer::after(Duration::from_millis(1700)).await;

    match mecanum.move_direction(MecanumDirection::Forward, 80) {
        Ok(_) => info!("  ✅ 直走成功"),
        Err(e) => error!("  ❌ 精细右平移失败: {:?}", e),
    }
    Timer::after(Duration::from_millis(2000)).await;

    match mecanum.strafe_with_speed(MecanumDirection::Right, 55, 80) {
        Ok(_) => info!("  ✅ 精细左平移成功"),
        Err(e) => error!("  ❌ 精细右平移失败: {:?}", e),
    }
    Timer::after(Duration::from_millis(1700)).await;

    match mecanum.move_direction(MecanumDirection::Forward, 80) {
        Ok(_) => info!("  ✅ 直走成功"),
        Err(e) => error!("  ❌ 精细右平移失败: {:?}", e),
    }

    Timer::after(Duration::from_millis(2000)).await;

    match mecanum.strafe_with_speed(MecanumDirection::Left, 55, 80) {
        Ok(_) => info!("  ✅ 精细左平移成功"),
        Err(e) => error!("  ❌ 精细右平移失败: {:?}", e),
    }
    Timer::after(Duration::from_millis(1700)).await;

    match mecanum.move_direction(MecanumDirection::Forward, 80) {
        Ok(_) => info!("  ✅ 直走成功"),
        Err(e) => error!("  ❌ 精细右平移失败: {:?}", e),
    }
    mecanum.stop_all().unwrap();
}

// async fn task1() {
//     match mecanum.move_direction(MecanumDirection::Forward, 80) {
//         Ok(_) => info!("  ✅ 直走成功"),
//         Err(e) => error!("  ❌ 精细右平移失败: {:?}", e),
//     }
//     Timer::after(Duration::from_millis(6000)).await;

//     match mecanum.strafe_with_speed(MecanumDirection::Left, 60, 80) {
//         Ok(_) => info!("  ✅ 精细左平移成功"),
//         Err(e) => error!("  ❌ 精细右平移失败: {:?}", e),
//     }
//     Timer::after(Duration::from_millis(1700)).await;

//     match mecanum.move_direction(MecanumDirection::Forward, 80) {
//         Ok(_) => info!("  ✅ 直走成功"),
//         Err(e) => error!("  ❌ 精细右平移失败: {:?}", e),
//     }
//     Timer::after(Duration::from_millis(1000)).await;

//     mecanum.stop_all().unwrap();
// }
