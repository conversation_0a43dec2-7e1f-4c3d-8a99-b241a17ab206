#![no_std]
#![no_main]

mod drv8833;
mod motor_driver;
mod tasks;
mod tm6612;
use defmt::*;
use drv8833::Drv8833Single;
use embassy_executor::Spawner;
use embassy_stm32::Config;
use embassy_stm32::gpio::{Input, Level, Output, Speed};
use embassy_stm32::time::Hertz;
use embassy_stm32::timer::simple_pwm::{PwmPin, SimplePwm};
use embassy_time::{Duration, Timer};
use motor_driver::{Drv8833Wrapper, MecanumDirection, MecanumDrive, Tm6612Wrapper};
use tasks::*;
use tm6612::Tm6612Single;
use {defmt_rtt as _, panic_probe as _};

use embassy_stm32::gpio::Pull;
use embassy_time::{Instant, block_for};

/// 运动控制参数
#[derive(<PERSON>lone, <PERSON><PERSON>)]
struct MotionParams {
    direction: MecanumDirection,
    speed: u8,
    duration_ms: u64,
    success_msg: &'static str,
}

/// 平移控制参数
#[derive(Clone, Copy)]
struct StrafeParams {
    direction: MecanumDirection,
    front_speed: u8,
    back_speed: u8,
    duration_ms: u64,
    success_msg: &'static str,
}

/// 运动序列类型
enum MotionSequence {
    Move(MotionParams),
    Strafe(StrafeParams),
}

// 常用运动模式常量
const MISSION1_FORWARD: MotionParams = MotionParams {
    direction: MecanumDirection::Forward,
    speed: 80,
    duration_ms: 6000,
    success_msg: "直走成功",
};

const MISSION1_LEFT: StrafeParams = StrafeParams {
    direction: MecanumDirection::Left,
    front_speed: 60,
    back_speed: 80,
    duration_ms: 1700,
    success_msg: "精细左平移成功",
};

const MISSION1_FORWARD2: MotionParams = MotionParams {
    direction: MecanumDirection::Forward,
    speed: 80,
    duration_ms: 1000,
    success_msg: "直走成功",
};

// 常用运动模式常量
const FORWARD_LONE_LONE_MOTION: MotionParams = MotionParams {
    direction: MecanumDirection::Forward,
    speed: 77,
    duration_ms: 7000,
    success_msg: "直走成功",
};

const FORWARD_LONE_MOTION: MotionParams = MotionParams {
    direction: MecanumDirection::Forward,
    speed: 77,
    duration_ms: 2000,
    success_msg: "直走成功",
};

// 常用运动模式常量
const FORWARD_MOTION: MotionParams = MotionParams {
    direction: MecanumDirection::Forward,
    speed: 77,
    duration_ms: 1500,
    success_msg: "直走成功",
};

// 常用运动模式常量
const BACKWORD_MOTION: MotionParams = MotionParams {
    direction: MecanumDirection::Backward,
    speed: 77,
    duration_ms: 1500,
    success_msg: "后退成功",
};

const LEFT_STRAFE: StrafeParams = StrafeParams {
    direction: MecanumDirection::Left,
    front_speed: 53,
    back_speed: 75,
    duration_ms: 2000,
    success_msg: "精细左平移成功",
};

const RIGHT_STRAFE: StrafeParams = StrafeParams {
    direction: MecanumDirection::Right,
    front_speed: 50,
    back_speed: 75,
    duration_ms: 1900,
    success_msg: "精细右平移成功",
};

/// 执行运动序列的辅助函数
async fn execute_sequence<FL, FR, BL, BR>(
    mecanum: &mut MecanumDrive<FL, FR, BL, BR>,
    sequence: &[MotionSequence],
) where
    FL: motor_driver::MotorDriver,
    FR: motor_driver::MotorDriver,
    BL: motor_driver::MotorDriver,
    BR: motor_driver::MotorDriver,
{
    for motion in sequence {
        match motion {
            MotionSequence::Move(params) => {
                execute_motion(mecanum, *params).await;
            }
            MotionSequence::Strafe(params) => {
                execute_strafe(mecanum, *params).await;
            }
        }
    }
}

/// 执行运动控制并处理错误的辅助函数
async fn execute_motion<FL, FR, BL, BR>(
    mecanum: &mut MecanumDrive<FL, FR, BL, BR>,
    params: MotionParams,
) where
    FL: motor_driver::MotorDriver,
    FR: motor_driver::MotorDriver,
    BL: motor_driver::MotorDriver,
    BR: motor_driver::MotorDriver,
{
    match mecanum.move_direction(params.direction, params.speed) {
        Ok(_) => info!("  ✅ {}", params.success_msg),
        Err(e) => error!("  ❌ 运动失败: {:?}", e),
    }
    Timer::after(Duration::from_millis(params.duration_ms)).await;
}

/// 执行平移控制并处理错误的辅助函数
async fn execute_strafe<FL, FR, BL, BR>(
    mecanum: &mut MecanumDrive<FL, FR, BL, BR>,
    params: StrafeParams,
) where
    FL: motor_driver::MotorDriver,
    FR: motor_driver::MotorDriver,
    BL: motor_driver::MotorDriver,
    BR: motor_driver::MotorDriver,
{
    match mecanum.strafe_with_speed(params.direction, params.front_speed, params.back_speed) {
        Ok(_) => info!("  ✅ {}", params.success_msg),
        Err(e) => error!("  ❌ 平移失败: {:?}", e),
    }
    Timer::after(Duration::from_millis(params.duration_ms)).await;
}

#[embassy_executor::main]
async fn main(_spawner: Spawner) {
    let p = embassy_stm32::init(Config::default());

    info!("🚗 四轮麦克纳姆驱动系统初始化开始");

    // 前轮GPIO引脚初始化
    let fl_in1 = Output::new(p.PA4, Level::Low, Speed::Low);
    let fl_in2 = Output::new(p.PA5, Level::Low, Speed::Low);
    let fl_pwm_pin = PwmPin::new(p.PB0, embassy_stm32::gpio::OutputType::PushPull);

    let fr_in1 = Output::new(p.PA7, Level::Low, Speed::Low);
    let fr_in2 = Output::new(p.PA6, Level::Low, Speed::Low);
    let fr_pwm_pin = PwmPin::new(p.PB1, embassy_stm32::gpio::OutputType::PushPull);

    // 后轮PWM引脚初始化
    let bl_pwm_pin1 = PwmPin::new(p.PA1, embassy_stm32::gpio::OutputType::PushPull);
    let bl_pwm_pin2 = PwmPin::new(p.PA0, embassy_stm32::gpio::OutputType::PushPull);

    let br_pwm_pin1 = PwmPin::new(p.PA3, embassy_stm32::gpio::OutputType::PushPull);
    let br_pwm_pin2 = PwmPin::new(p.PA2, embassy_stm32::gpio::OutputType::PushPull);

    // PWM定时器初始化 - 使用统一的频率配置
    let pwm_frequency = Hertz(20_000);

    let pwm_tim2 = SimplePwm::new(
        p.TIM2,
        Some(bl_pwm_pin2),
        Some(bl_pwm_pin1),
        Some(br_pwm_pin2),
        Some(br_pwm_pin1),
        pwm_frequency,
        Default::default(),
    );

    let pwm_tim3 = SimplePwm::new(
        p.TIM3,
        None,
        None,
        Some(fl_pwm_pin),
        Some(fr_pwm_pin),
        pwm_frequency,
        Default::default(),
    );

    let tim3_chs = pwm_tim3.split();
    let tim2_chs = pwm_tim2.split();

    // PWM通道分配和启用
    let mut fl_pwm = tim3_chs.ch3;
    let mut fr_pwm = tim3_chs.ch4;
    let mut bl_pwm_in1 = tim2_chs.ch2;
    let mut bl_pwm_in2 = tim2_chs.ch1;
    let mut br_pwm_in1 = tim2_chs.ch4;
    let mut br_pwm_in2 = tim2_chs.ch3;

    // 批量启用PWM通道 - 分别处理不同定时器
    // TIM3通道 (前轮)
    fl_pwm.enable();
    fr_pwm.enable();

    // TIM2通道 (后轮)
    bl_pwm_in1.enable();
    bl_pwm_in2.enable();
    br_pwm_in1.enable();
    br_pwm_in2.enable();

    let front_left_tm = Tm6612Single::new(fl_in1, fl_in2, fl_pwm);
    let front_right_tm = Tm6612Single::new(fr_in1, fr_in2, fr_pwm);
    let back_left_drv = Drv8833Single::new(bl_pwm_in2, bl_pwm_in1);
    let back_right_drv = Drv8833Single::new(br_pwm_in1, br_pwm_in2);

    let front_left = Tm6612Wrapper::new(front_left_tm);
    let front_right = Tm6612Wrapper::new(front_right_tm);
    let back_left = Drv8833Wrapper::new(back_left_drv);
    let back_right = Drv8833Wrapper::new(back_right_drv);

    let mut mecanum: MecanumDrive<
        Tm6612Wrapper<'_, embassy_stm32::peripherals::TIM3>,
        Tm6612Wrapper<'_, embassy_stm32::peripherals::TIM3>,
        Drv8833Wrapper<embassy_stm32::peripherals::TIM2>,
        Drv8833Wrapper<embassy_stm32::peripherals::TIM2>,
    > = MecanumDrive::new(front_left, front_right, back_left, back_right);

    info!("✅ 四轮麦克纳姆驱动系统初始化完成");
    info!("🔧 前轮使用TB6612，后轮使用DRV8833");
    info!("🎯 开始运动演示...");

    let m1_sequence = [
        MotionSequence::Move(MISSION1_FORWARD),
        MotionSequence::Strafe(MISSION1_LEFT),
        MotionSequence::Move(MISSION1_FORWARD2),
    ];

    let m2_sequence = [
        MotionSequence::Move(FORWARD_LONE_MOTION),
        MotionSequence::Strafe(LEFT_STRAFE),
        MotionSequence::Move(FORWARD_MOTION),
        MotionSequence::Strafe(RIGHT_STRAFE),
        MotionSequence::Move(FORWARD_MOTION),
        MotionSequence::Strafe(LEFT_STRAFE),
        MotionSequence::Move(FORWARD_LONE_MOTION),
    ];

    // 定义运动序列 - 使用预定义常量简化代码
    let m3_sequence = [
        MotionSequence::Move(FORWARD_LONE_MOTION),
        MotionSequence::Strafe(LEFT_STRAFE),
        MotionSequence::Move(FORWARD_MOTION),
        MotionSequence::Strafe(RIGHT_STRAFE),
        MotionSequence::Strafe(RIGHT_STRAFE),
        MotionSequence::Move(BACKWORD_MOTION),
        MotionSequence::Strafe(LEFT_STRAFE),
        MotionSequence::Strafe(LEFT_STRAFE),
        MotionSequence::Move(FORWARD_LONE_LONE_MOTION),
    ];

    let button = Input::new(p.PA15, embassy_stm32::gpio::Pull::Up);
    let leve = button.get_level();
    info!("leve:{}", leve);

    // 等待按键按下次数决定使用哪个序列
    info!("请按下按钮选择运动序列：1次为motion_sequence，2次为m1_sequence");

    let mut count = 0;
    let mut last_state = button.is_low();
    let mut pressed = false;

    while button.is_high() {}
    let start = Instant::now();
    while Instant::now() - start < Duration::from_secs(2) {
        let is_pressed = button.is_low();

        // 按键由未按变为按下
        if is_pressed && !last_state && !pressed {
            count += 1;
            pressed = true;
        }

        // 按键松开后允许再次计数
        if !is_pressed {
            pressed = false;
        }

        last_state = is_pressed;

        // 简单去抖：每次检测后等待 20ms
        block_for(Duration::from_millis(20));
    }

    info!("count:{}", count);
    match count {
        1 => {
            execute_sequence(&mut mecanum, &m1_sequence).await;
        }

        2 => {
            execute_sequence(&mut mecanum, &m2_sequence).await;
        }
        3 => {
            execute_sequence(&mut mecanum, &m3_sequence).await;
        }
        _ => {}
    };

    Timer::after_secs(1).await;

    mecanum.stop_all().unwrap();
}
