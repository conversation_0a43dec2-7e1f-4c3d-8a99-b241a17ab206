# TB6612 (TM6612) 使用指南

## 🔧 TB6612 vs DRV8833 控制差异

### DRV8833 控制方式
```
控制方式: PWM直接控制IN1/IN2
前进: IN1=PWM(speed%), IN2=0%
后退: IN1=0%, IN2=PWM(speed%)
刹车: IN1=100%, IN2=100%
滑行: IN1=0%, IN2=0%
```

### TB6612 控制方式
```
控制方式: 数字信号控制方向 + 独立PWM控制速度
前进: IN1=HIGH, IN2=LOW, PWM=speed%
后退: IN1=LOW, IN2=HIGH, PWM=speed%
刹车: IN1=HIGH, IN2=HIGH, PWM=任意
滑行: IN1=LOW, IN2=LOW, PWM=0%
```

## 📍 引脚连接

### 单个TB6612电机连接
```
STM32引脚    TB6612引脚    功能
--------------------------------
PB0      ->  AIN1        方向控制1 (数字输出)
PB1      ->  AIN2        方向控制2 (数字输出)
PA6      ->  PWMA        速度控制 (PWM输出)
GND      ->  GND         地线
3.3V     ->  VCC         逻辑电源
6-12V    ->  VM          电机电源
```

### 双电机TB6612连接 (本项目配置)
```
电机    方向控制引脚    PWM控制引脚    定时器通道
------------------------------------------------
电机A   PB0, PB1      PA6          TIM3_CH1
电机B   PB10, PB11    PA7          TIM3_CH2
```

## 💻 代码实现

### TB6612驱动器结构
```rust
pub struct Tm6612Single<'a, TIM: GeneralInstance4Channel> {
    pub in1: Output<'a>,      // 方向控制引脚1
    pub in2: Output<'a>,      // 方向控制引脚2
    pub pwm: SimplePwmChannel<'static, TIM>,  // PWM速度控制
}
```

### 初始化代码
```rust
// 创建方向控制引脚 (数字输出)
let in1 = Output::new(p.PB0, Level::Low, Speed::Low);
let in2 = Output::new(p.PB1, Level::Low, Speed::Low);

// 创建PWM引脚
let pwm_pin = PwmPin::new(p.PA6, embassy_stm32::gpio::OutputType::PushPull);

// 创建PWM实例
let pwm = SimplePwm::new(p.TIM3, Some(pwm_pin), None, None, None, 
                        Hertz(10_000), Default::default());
let chs = pwm.split();
let mut pwm_ch = chs.ch1;
pwm_ch.enable();

// 创建TB6612驱动器
let mut motor = Tm6612Single::new(in1, in2, pwm_ch);
```

### 控制代码
```rust
// 前进 50% 速度
motor.set_motor(Direction::Forward, 50)?;

// 后退 30% 速度  
motor.set_motor(Direction::Backward, 30)?;

// 刹车
motor.set_motor(Direction::Brake, 0)?;

// 滑行停止
motor.coast()?;
```

## 🧪 测试程序

### 运行TB6612专用测试
```bash
cargo run --bin tb6612_test
```

这个测试程序会：
1. 初始化TB6612驱动器
2. 测试前进/后退/刹车/滑行
3. 测试不同速度档位
4. 显示详细的状态信息

### 测试输出示例
```
🔧 TB6612 驱动器测试开始
✅ TB6612 初始化完成
📍 引脚配置:
  - IN1: PB0 (方向控制)
  - IN2: PB1 (方向控制)  
  - PWM: PA6 (速度控制)
🔄 前进测试 (30% 速度)
  ✅ 前进成功
⏹️ 停止
🔄 后退测试 (30% 速度)
  ✅ 后退成功
```

## ⚡ 性能特点

### TB6612优势
- **独立速度控制**: PWM信号专门控制速度，更精确
- **更高电流**: 支持更大功率的电机
- **更好的散热**: 内置过热保护
- **更稳定**: 方向切换时速度控制更平滑

### 注意事项
1. **引脚数量**: TB6612需要3个引脚/电机 (2个数字+1个PWM)
2. **电源要求**: 逻辑电源3.3V，电机电源6-12V
3. **电流限制**: 每通道最大1.2A连续电流
4. **散热**: 高负载时需要适当散热

## 🔄 与DRV8833混用

在本项目中，我们成功实现了DRV8833和TB6612的混合使用：
- **前轮**: 使用DRV8833 (PWM直接控制)
- **后轮**: 使用TB6612 (独立PWM控制)
- **统一接口**: 通过MotorDriver trait实现统一控制

这种混合方案的优势：
- 充分利用现有硬件
- 保持代码的一致性
- 易于维护和扩展

## 🚀 扩展应用

基于TB6612的特性，可以实现：
- **精确速度控制**: 利用独立PWM实现更精确的速度调节
- **平滑加减速**: 通过渐变PWM实现平滑的速度变化
- **电流监控**: 利用TB6612的电流反馈功能
- **故障检测**: 利用内置的保护功能进行故障诊断
