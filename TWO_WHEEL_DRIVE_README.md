# 两轮DRV8833驱动示例

这个示例展示了如何使用DRV8833双H桥电机驱动器来控制两轮差分驱动机器人。

## 硬件连接

### STM32F103C8T6 引脚连接

```
STM32 引脚    DRV8833 引脚    功能
---------------------------------
PA0      ->   AIN1         左轮电机 IN1
PA1      ->   AIN2         左轮电机 IN2
PA2      ->   BIN1         右轮电机 IN1
PA3      ->   BIN2         右轮电机 IN2
GND      ->   GND          地线
3.3V     ->   VCC          逻辑电源
```

### DRV8833 到电机连接

```
DRV8833 引脚    电机连接
-----------------------
AOUT1       ->  左轮电机 +
AOUT2       ->  左轮电机 -
BOUT1       ->  右轮电机 +
BOUT2       ->  右轮电机 -
VMOT        ->  电机电源 (6-10.8V)
```

## 运动模式

这个示例演示了以下运动模式：

### 1. 直线运动
- **前进**: 两轮同时向前转动 (50% 速度)
- **后退**: 两轮同时向后转动 (50% 速度)

### 2. 原地转向
- **右转**: 左轮前进，右轮后退 (40% 速度)
- **左转**: 左轮后退，右轮前进 (40% 速度)

### 3. 弧线转向
- **大弧度右转**: 左轮快速前进 (70%)，右轮慢速前进 (30%)
- **大弧度左转**: 左轮慢速前进 (30%)，右轮快速前进 (70%)

### 4. 停止模式
- **滑行停止**: 两轮都设置为滑行模式 (coast)
- **刹车停止**: 两轮都设置为刹车模式 (brake)

## 代码特点

1. **使用单个定时器**: 所有PWM信号都使用TIM2的4个通道
2. **错误处理**: 每个电机操作都有适当的错误处理
3. **清晰的日志**: 使用defmt提供详细的运行状态信息
4. **模块化设计**: 每个电机都是独立的Drv8833Single实例

## DRV8833 工作原理

DRV8833使用以下控制方式：
- **前进**: IN1=PWM(速度), IN2=0%
- **后退**: IN1=0%, IN2=PWM(速度)
- **刹车**: IN1=100%, IN2=100%
- **滑行**: IN1=0%, IN2=0%

## 编译和运行

```bash
# 编译
cargo build

# 烧录到STM32
cargo run
```

## 注意事项

1. **电源要求**: 确保为DRV8833提供适当的电机电源电压 (6-10.8V)
2. **电流限制**: DRV8833每通道最大电流为1.2A
3. **散热**: 在高负载下使用时考虑散热
4. **接线检查**: 确保所有连接正确，特别是电源和地线

## 扩展功能

可以基于这个示例添加：
- 速度控制算法
- PID控制器
- 传感器反馈 (编码器、陀螺仪等)
- 遥控功能
- 自动导航
