// use embassy_executor::task;
// use embassy_sync::{blocking_mutex::raw::CriticalSectionRawMutex, mutex::Mutex};
// use embassy_time::{Duration, Timer};

// use crate::hardware::{DualTrackingSensor, ImuInterface, Mpu6500Sensor, SensorInterface};

// /// 共享的小车状态
// pub static SHARED_CAR_STATE: Mutex<CriticalSectionRawMutex, Option<CarState>> = Mutex::new(None);

// /// 传感器任务
// #[task]
// pub async fn sensor_task(mut tracking_sensor: DualTrackingSensor, mut imu_sensor: Mpu6500Sensor) {
//     defmt::info!("Sensor task started");

//     let sample_interval = Duration::from_hz(u64::from(SENSOR_SAMPLE_RATE));

//     loop {
//         // 读取循迹传感器
//         let tracking_data = tracking_sensor.read().await;
//         let line_state = tracking_data.to_line_state();

//         // 读取IMU数据
//         let angles = match imu_sensor.read_angles().await {
//             Ok(angles) => angles,
//             Err(e) => {
//                 defmt::warn!("IMU read error: {:?}", e);
//                 Angles::default()
//             }
//         };

//         // 更新共享状态
//         let car_state = CarState {
//             line_state,
//             angles,
//             speed: crate::config::DEFAULT_SPEED, // 这里可以从其他地方获取实际速度
//             position: None,                      // 位置检测需要额外逻辑
//         };

//         // 更新共享状态
//         SHARED_CAR_STATE.lock().await.replace(car_state);

//         Timer::after(sample_interval).await;
//     }
// }

// /// 获取当前小车状态
// pub async fn get_car_state() -> Option<CarState> {
//     *SHARED_CAR_STATE.lock().await
// }
