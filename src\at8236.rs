//! AT8236 双 H 桥电机驱动器
//!
//! AT8236 是一个双路 H 桥电机驱动芯片，可以控制两个直流电机。
//! 与 DRV8833 类似，但可能有不同的引脚配置和特性。
//!
//! # AT8236 工作原理
//!
//! ```text
//! 电机控制方式:
//! - 前进: IN1=PWM(speed), IN2=0%
//! - 后退: IN1=0%, IN2=PWM(speed)  
//! - 刹车: IN1=100%, IN2=100%
//! - 滑行: IN1=0%, IN2=0%
//! ```
//!
//! # 硬件连接
//!
//! 每个电机需要 2 个 PWM 引脚：
//! ```text
//! STM32    AT8236     Motor
//! PA0  ->  AIN1   ->   M1+
//! PA1  ->  AIN2   ->   M1-
//! PA2  ->  BIN1   ->   M2+
//! PA3  ->  BIN2   ->   M2-
//! ```

use embassy_stm32::timer::GeneralInstance4Channel;
use embassy_stm32::timer::simple_pwm::SimplePwmChannel;

/// AT8236 驱动错误
#[derive(Debug, <PERSON><PERSON>, Copy, PartialEq, Eq)]
#[cfg_attr(feature = "defmt", derive(defmt::Format))]
pub enum At8236Error {
    /// 无效的速度值 (必须在 0-100 之间)
    InvalidSpeed,
}

/// 电机方向
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum Direction {
    /// 前进
    Forward,
    /// 后退
    Backward,
    /// 刹车 (两引脚都为高电平)
    Brake,
    /// 滑行 (两引脚都为低电平)
    Coast,
}

/// AT8236 单电机驱动器 - 支持不同定时器的 PWM 通道
///
/// 这个结构体实现了 AT8236 的控制方式：
/// - 使用两个 PWM 通道 (IN1, IN2)
/// - **支持来自不同定时器的 PWM 通道**
/// - 通过 PWM 占空比控制速度和方向
///
/// # 控制原理
/// ```text
/// 前进: IN1=PWM(speed), IN2=0%
/// 后退: IN1=0%, IN2=PWM(speed)
/// 刹车: IN1=100%, IN2=100%
/// 滑行: IN1=0%, IN2=0%
/// ```
///
/// # 用法
/// ```rust,no_run
/// // 相同定时器的通道
/// let motor = At8236Single::new(tim1_ch1, tim1_ch2);
///
/// // 不同定时器的通道 (新功能!)
/// let motor = At8236Single::new(tim1_ch1, tim3_ch2);
///
/// motor.set_motor(Direction::Forward, 50)?; // 前进 50% 速度
/// motor.set_motor(Direction::Backward, 75)?; // 后退 75% 速度
/// motor.brake()?; // 刹车
/// motor.coast()?; // 滑行
/// ```
pub struct At8236Single<TIM1: GeneralInstance4Channel, TIM2: GeneralInstance4Channel> {
    in1: SimplePwmChannel<'static, TIM1>,
    in2: SimplePwmChannel<'static, TIM2>,
}

impl<TIM1: GeneralInstance4Channel, TIM2: GeneralInstance4Channel> At8236Single<TIM1, TIM2> {
    /// 创建新的 AT8236 单电机驱动器 - 支持不同定时器
    ///
    /// # 参数
    /// * `in1` - 电机 IN1 引脚的 PWM 通道 (可以来自任何定时器)
    /// * `in2` - 电机 IN2 引脚的 PWM 通道 (可以来自任何定时器)
    ///
    /// # 灵活性示例
    /// ```rust,no_run
    /// // 相同定时器
    /// let motor1 = At8236Single::new(tim1_ch1, tim1_ch2);
    ///
    /// // 不同定时器 - 更大灵活性!
    /// let motor2 = At8236Single::new(tim1_ch1, tim3_ch2);
    /// let motor3 = At8236Single::new(tim2_ch1, tim4_ch3);
    /// ```
    pub fn new(in1: SimplePwmChannel<'static, TIM1>, in2: SimplePwmChannel<'static, TIM2>) -> Self {
        Self { in1, in2 }
    }

    /// 设置电机方向和速度
    ///
    /// # 参数
    /// * `direction` - 电机方向
    /// * `speed` - 速度百分比 (0-100)
    pub fn set_motor(&mut self, direction: Direction, speed: u8) -> Result<(), At8236Error> {
        if speed > 100 {
            return Err(At8236Error::InvalidSpeed);
        }

        match direction {
            Direction::Forward => {
                let max_duty = self.in1.max_duty_cycle();
                let duty = (max_duty as u32 * speed as u32) / 100;
                self.in1.set_duty_cycle(duty as u16);
                self.in2.set_duty_cycle(0);
            }
            Direction::Backward => {
                let max_duty = self.in2.max_duty_cycle();
                let duty = (max_duty as u32 * speed as u32) / 100;
                self.in1.set_duty_cycle(0);
                self.in2.set_duty_cycle(duty as u16);
            }
            Direction::Brake => {
                let max_duty = self.in1.max_duty_cycle();
                self.in1.set_duty_cycle(max_duty);
                self.in2.set_duty_cycle(max_duty);
            }
            Direction::Coast => {
                self.in1.set_duty_cycle(0);
                self.in2.set_duty_cycle(0);
            }
        }

        Ok(())
    }

    /// 刹车电机
    pub fn brake(&mut self) -> Result<(), At8236Error> {
        self.set_motor(Direction::Brake, 0)
    }

    /// 滑行停止电机
    pub fn coast(&mut self) -> Result<(), At8236Error> {
        self.set_motor(Direction::Coast, 0)
    }

    /// 获取 IN1 PWM 通道的引用
    pub fn in1_pwm(&mut self) -> &mut SimplePwmChannel<'static, TIM1> {
        &mut self.in1
    }

    /// 获取 IN2 PWM 通道的引用
    pub fn in2_pwm(&mut self) -> &mut SimplePwmChannel<'static, TIM2> {
        &mut self.in2
    }
}

/// AT8236 双电机驱动器 - 支持灵活的定时器配置
///
/// 控制一个 AT8236 芯片的两个电机 (Motor A + Motor B)
/// 每个电机的两个 PWM 通道可以来自不同的定时器
///
/// # 硬件连接
/// - Motor A: AIN1, AIN2 (可以来自不同定时器)
/// - Motor B: BIN1, BIN2 (可以来自不同定时器)
///
/// # 用法
/// ```rust,no_run
/// // 传统用法 (相同定时器)
/// let driver = At8236Dual::new(tim1_ch1, tim1_ch2, tim1_ch3, tim1_ch4);
///
/// // 灵活用法 (不同定时器)
/// let driver = At8236Dual::new(tim1_ch1, tim2_ch1, tim3_ch1, tim4_ch1);
///
/// driver.set_motor_a(Direction::Forward, 50)?; // Motor A 前进 50%
/// driver.set_motor_b(Direction::Backward, 75)?; // Motor B 后退 75%
/// ```
pub struct At8236Dual<
    TIM1: GeneralInstance4Channel,
    TIM2: GeneralInstance4Channel,
    TIM3: GeneralInstance4Channel,
    TIM4: GeneralInstance4Channel,
> {
    motor_a: At8236Single<TIM1, TIM2>,
    motor_b: At8236Single<TIM3, TIM4>,
}

impl<
    TIM1: GeneralInstance4Channel,
    TIM2: GeneralInstance4Channel,
    TIM3: GeneralInstance4Channel,
    TIM4: GeneralInstance4Channel,
> At8236Dual<TIM1, TIM2, TIM3, TIM4>
{
    /// 创建新的 AT8236 双电机驱动器 - 支持灵活的定时器配置
    ///
    /// # 参数
    /// * `ain1` - Motor A IN1 引脚的 PWM 通道 (可以来自任何定时器)
    /// * `ain2` - Motor A IN2 引脚的 PWM 通道 (可以来自任何定时器)
    /// * `bin1` - Motor B IN1 引脚的 PWM 通道 (可以来自任何定时器)
    /// * `bin2` - Motor B IN2 引脚的 PWM 通道 (可以来自任何定时器)
    ///
    /// # 灵活性示例
    /// ```rust,no_run
    /// // 所有通道来自同一定时器
    /// let dual = At8236Dual::new(tim1_ch1, tim1_ch2, tim1_ch3, tim1_ch4);
    ///
    /// // 每个电机使用不同定时器
    /// let dual = At8236Dual::new(tim1_ch1, tim1_ch2, tim3_ch1, tim3_ch2);
    ///
    /// // 完全分散的定时器配置
    /// let dual = At8236Dual::new(tim1_ch1, tim2_ch1, tim3_ch1, tim4_ch1);
    /// ```
    pub fn new(
        ain1: SimplePwmChannel<'static, TIM1>,
        ain2: SimplePwmChannel<'static, TIM2>,
        bin1: SimplePwmChannel<'static, TIM3>,
        bin2: SimplePwmChannel<'static, TIM4>,
    ) -> Self {
        Self {
            motor_a: At8236Single::new(ain1, ain2),
            motor_b: At8236Single::new(bin1, bin2),
        }
    }

    /// 设置 Motor A 的方向和速度
    pub fn set_motor_a(&mut self, direction: Direction, speed: u8) -> Result<(), At8236Error> {
        self.motor_a.set_motor(direction, speed)
    }

    /// 设置 Motor B 的方向和速度
    pub fn set_motor_b(&mut self, direction: Direction, speed: u8) -> Result<(), At8236Error> {
        self.motor_b.set_motor(direction, speed)
    }

    /// 停止所有电机
    pub fn stop_all(&mut self) {
        let _ = self.motor_a.coast();
        let _ = self.motor_b.coast();
    }

    /// 刹车所有电机
    pub fn brake_all(&mut self) {
        let _ = self.motor_a.brake();
        let _ = self.motor_b.brake();
    }

    /// 获取 Motor A 的引用
    pub fn motor_a(&mut self) -> &mut At8236Single<TIM1, TIM2> {
        &mut self.motor_a
    }

    /// 获取 Motor B 的引用
    pub fn motor_b(&mut self) -> &mut At8236Single<TIM3, TIM4> {
        &mut self.motor_b
    }
}

// 为 AT8236Single 实现 MotorDriver trait，使其与 DRV8833Single 兼容
impl<TIM1: GeneralInstance4Channel, TIM2: GeneralInstance4Channel> crate::MotorDriver
    for At8236Single<TIM1, TIM2>
{
    type Error = At8236Error;

    fn set_motor(&mut self, direction: crate::Direction, speed: u8) -> Result<(), Self::Error> {
        // 转换方向枚举
        let at8236_direction = match direction {
            crate::Direction::Forward => Direction::Forward,
            crate::Direction::Backward => Direction::Backward,
            crate::Direction::Brake => Direction::Brake,
            crate::Direction::Coast => Direction::Coast,
        };

        self.set_motor(at8236_direction, speed)
            .map_err(|_| At8236Error::InvalidSpeed)
    }

    fn brake(&mut self) -> Result<(), Self::Error> {
        self.brake()
    }

    fn coast(&mut self) -> Result<(), Self::Error> {
        self.coast()
    }
}
