//! TM6612 (TB6612) 双 H 桥电机驱动器实现
//!
//! TB6612 使用独立的PWM引脚进行速度控制：
//! - IN1, IN2: 方向控制 (数字信号)
//! - PWM: 速度控制 (PWM信号)

use embassy_stm32::gpio::Output;
use embassy_stm32::timer::GeneralInstance4Channel;
use embassy_stm32::timer::simple_pwm::SimplePwmChannel;

/// TM6612 驱动错误
#[derive(Debug, Clone, Copy, PartialEq, Eq, defmt::Format)]
pub enum Error {
    /// 无效的速度值 (必须在 0-100 之间)
    InvalidSpeed,
}

/// 电机方向
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum Direction {
    /// 前进
    Forward,
    /// 后退
    Backward,
    /// 刹车 (两引脚都为高电平)
    Brake,
    /// 滑行 (两引脚都为低电平)
    Coast,
}

/// TM6612 单电机驱动器
///
/// TB6612 的控制方式：
/// - IN1, IN2: 方向控制引脚 (数字输出)
/// - PWM: 速度控制引脚 (PWM输出)
///
/// # 控制原理
/// ```text
/// 前进: IN1=HIGH, IN2=LOW, PWM=speed%
/// 后退: IN1=LOW, IN2=HIGH, PWM=speed%
/// 刹车: IN1=HIGH, IN2=HIGH, PWM=任意
/// 滑行: IN1=LOW, IN2=LOW, PWM=任意
/// ```
pub struct Tm6612Single<'a, TIM: GeneralInstance4Channel> {
    pub in1: Output<'a>,
    pub in2: Output<'a>,
    pub pwm: SimplePwmChannel<'static, TIM>,
}

impl<'a, TIM: GeneralInstance4Channel> Tm6612Single<'a, TIM> {
    /// 创建新的 TM6612 单电机驱动器
    ///
    /// # 参数
    /// * `in1` - 电机 IN1 方向控制引脚
    /// * `in2` - 电机 IN2 方向控制引脚
    /// * `pwm` - 电机 PWM 速度控制通道
    pub fn new(in1: Output<'a>, in2: Output<'a>, pwm: SimplePwmChannel<'static, TIM>) -> Self {
        Self { in1, in2, pwm }
    }

    /// 设置电机方向和速度
    ///
    /// # 参数
    /// * `direction` - 电机方向
    /// * `speed` - 速度百分比 (0-100)
    pub fn set_motor(&mut self, direction: Direction, speed: u8) -> Result<(), Error> {
        if speed > 100 {
            return Err(Error::InvalidSpeed);
        }

        // 设置PWM速度 (除了滑行模式，其他模式都需要PWM)
        let max_duty = self.pwm.max_duty_cycle();
        let duty = if direction == Direction::Coast {
            0 // 滑行模式PWM为0
        } else {
            (max_duty as u32 * speed as u32) / 100
        };
        self.pwm.set_duty_cycle(duty as u16);

        // 设置方向控制引脚
        match direction {
            Direction::Forward => {
                // 前进: IN1=HIGH, IN2=LOW
                self.in1.set_high();
                self.in2.set_low();
            }
            Direction::Backward => {
                // 后退: IN1=LOW, IN2=HIGH
                self.in1.set_low();
                self.in2.set_high();
            }
            Direction::Brake => {
                // 刹车: IN1=HIGH, IN2=HIGH
                self.in1.set_high();
                self.in2.set_high();
            }
            Direction::Coast => {
                // 滑行: IN1=LOW, IN2=LOW
                self.in1.set_low();
                self.in2.set_low();
            }
        }

        Ok(())
    }

    /// 刹车电机
    pub fn brake(&mut self) -> Result<(), Error> {
        self.set_motor(Direction::Brake, 0)
    }

    /// 滑行停止电机
    pub fn coast(&mut self) -> Result<(), Error> {
        self.set_motor(Direction::Coast, 0)
    }
}
