{
    // See https://go.microsoft.com/fwlink/?LinkId=733558 
    // for the documentation about the tasks.json format
    "version": "2.0.0",
    "tasks": [
        {
            /*
             * This is the default cargo build task,
             * but we need to provide a label for it,
             * so we can invoke it from the debug launcher.
             */
            "label": "Cargo Build (debug)",
            "type": "process",
            "command": "cargo",
            "args": ["build"],
            "problemMatcher": [
                "$rustc"
            ],
            "group": {
                "kind": "build",
                "isDefault": true
            }
        },
        {
            "label": "Cargo Build (release)",
            "type": "process",
            "command": "cargo",
            "args": ["build", "--release"],
            "problemMatcher": [
                "$rustc"
            ],
            "group": "build"
        },
        {
            "label": "Cargo Build Examples (debug)",
            "type": "process",
            "command": "cargo",
            "args": ["build","--examples"],
            "problemMatcher": [
                "$rustc"
            ],
            "group": "build"
        },
        {
            "label": "Cargo Build Examples (release)",
            "type": "process",
            "command": "cargo",
            "args": ["build","--examples", "--release"],
            "problemMatcher": [
                "$rustc"
            ],
            "group": "build"
        },
        {
            "label": "Cargo Clean",
            "type": "process",
            "command": "cargo",
            "args": ["clean"],
            "problemMatcher": [],
            "group": "build"
        },
    ]
}
